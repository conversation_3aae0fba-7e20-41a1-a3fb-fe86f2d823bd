import 'package:flutter/material.dart';
import 'dart:io';
import 'dart:async';
import 'package:provider/provider.dart';
import '../services/theme_service.dart';
import '../services/app_version_service.dart'; // Import the AppVersionService
import '../theme/app_theme.dart';
import '../theme/design_tokens.dart';
import '../theme/responsive_breakpoints.dart';
import '../theme/semantic_colors.dart';
import '../theme/app_typography.dart';
import '../widgets/adaptive_navigation.dart';
import '../widgets/custom_title_bar.dart';
import '../widgets/quiz/quiz_progress_widget.dart';
import '../widgets/whats_new_dialog.dart'; // Import the WhatsNewDialog
import 'plan_comptable_screen.dart';
import 'references_screen.dart';
import 'settings_screen.dart';
import 'guide_screen.dart';
import 'tools_screen.dart';
import 'quiz/quiz_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  NavigationItem _selectedItem = NavigationItem.home;
  late final TabController _tabController;
  late final PageController _highlightsController;
  Timer? _highlightsTimer;
  int _currentHighlight = 0;

  final List<String> _categories = ['Comptabilité', 'Fiscalité'];

  final List<Map<String, String>> _highlights = [
    {
      'title': 'IS',
      'description':
          'Taux réduit de 15% pour les entreprises industrielles avec CA < 50 MDH',
    },
    {
      'title': 'TVA',
      'description':
          'Exonération des équipements destinés à l\'hémodialyse et au traitement du diabète',
    },
    {
      'title': 'IR',
      'description':
          'Relèvement du seuil d\'exonération de 30.000 à 40.000 DH pour les retraités',
    },
    {
      'title': 'Droits d\'Enregistrement',
      'description':
          'Extension des exonérations aux logements sociaux et logements à faible valeur',
    },
    {
      'title': 'Contribution Sociale',
      'description':
          'Nouvelle contribution sur les hauts revenus > 1 MDH par an',
    },
    {
      'title': 'TVA Immobilier',
      'description':
          'Application aux terrains à bâtir et constructions à usage professionnel',
    },
    {
      'title': 'Factures Électroniques',
      'description':
          'Obligation progressive à partir du 1er juillet 2024 selon le CA',
    },
    {
      'title': 'IR Agricole',
      'description':
          'Maintien de l\'exonération jusqu\'au 31/12/2025 pour CA < 5 MDH',
    },
    {
      'title': 'TVA Médicale',
      'description':
          'Exonération des dispositifs médicaux et équipements hospitaliers',
    },
    {
      'title': 'Amnistie Fiscale',
      'description':
          'Régularisation spontanée avec annulation des majorations jusqu\'au 31/12/2025',
    },
    {
      'title': 'IS PME',
      'description': 'Barème progressif pour les PME avec CA < 10 MDH',
    },
    {
      'title': 'TVA Éducation',
      'description':
          'Exonération des équipements éducatifs et matériel scolaire',
    },
    {
      'title': 'Délais TVA',
      'description': 'Nouveaux délais de dépôt selon le régime d\'imposition',
    },
    {
      'title': 'IR Automobile',
      'description':
          'Révision du barème de la taxe annuelle selon la puissance fiscale',
    },
    {
      'title': 'Contrôle Fiscal',
      'description':
          'Nouvelles procédures pour le contrôle sur pièces et sur place',
    },
    {
      'title': 'Taxe Carbone',
      'description':
          'Introduction progressive de la taxe carbone pour certains secteurs',
    },
    {
      'title': 'IR Digital',
      'description':
          'Nouveau régime pour les revenus des plateformes numériques',
    },
    {
      'title': 'TVA Export',
      'description':
          'Simplification des procédures de remboursement pour les exportateurs',
    },
    {
      'title': 'Droits de Timbre',
      'description': 'Révision des tarifs et dématérialisation des procédures',
    },
    {
      'title': 'Prix de Transfert',
      'description':
          'Nouvelles obligations documentaires pour les grandes entreprises',
    }
  ];

  late final AnimationController _tickerAnimationController;
  late final Animation<Offset> _tickerAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);

    // Setup ticker animation using design tokens
    _tickerAnimationController = AnimationController(
      duration: const Duration(seconds: 45), // Much slower animation
      vsync: this,
    )..repeat();

    _tickerAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: const Offset(-1.0, 0.0),
    ).animate(CurvedAnimation(
      parent: _tickerAnimationController,
      curve: Curves.linear,
    ));

    // Initialize controllers after build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _highlightsController = PageController(viewportFraction: 0.93);
      _startHighlightsTimer();
      
      // Check for app updates and show the "What's New" dialog if needed
      _checkForAppUpdates();
    });
  }
  
  /// Check if there's a new version of the app and show the "What's New" dialog if needed
  void _checkForAppUpdates() {
    final appVersionService = Provider.of<AppVersionService>(context, listen: false);
    
    // Use the new method to check if we should show the dialog
    if (appVersionService.initialized && appVersionService.shouldShowWhatsNew()) {
      // A small delay to ensure the UI is fully loaded
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _showWhatsNewDialog(appVersionService);
        }
      });
    }
  }
  
  /// Show the "What's New" dialog
  void _showWhatsNewDialog(AppVersionService appVersionService) {
    showDialog(
      context: context,
      barrierDismissible: false, // User must tap a button to close the dialog
      builder: (context) => WhatsNewDialog(
        appVersion: appVersionService.currentVersion ?? '2.4.1', // Use current version or fallback to a default
        onDismiss: () {
          // Mark this version as seen
          appVersionService.markVersionSeen();
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  void dispose() {
    _tickerAnimationController.dispose();
    _highlightsTimer?.cancel();
    _highlightsController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _startHighlightsTimer() {
    _highlightsTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!mounted) return;
      if (_highlightsController.hasClients) {
        if (_currentHighlight < _highlights.length - 1) {
          _currentHighlight++;
        } else {
          _currentHighlight = 0;
        }
        _highlightsController.animateToPage(
          _currentHighlight,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  Widget _buildBody() {
    switch (_selectedItem) {
      case NavigationItem.home:
        return _buildHomeContent();
      case NavigationItem.planComptable:
        return const PlanComptableScreen();
      case NavigationItem.guide:
        return const GuideScreen();
      case NavigationItem.tools:
        return const ToolsScreen();
      case NavigationItem.references:
        return const ReferencesScreen();
      case NavigationItem.quiz:
        return const QuizScreen();
      case NavigationItem.settings:
        return const SettingsScreen();
    }
  }

  Widget _buildHomeContent() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      decoration: AppTheme.getGradientDecoration(colorScheme),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            _buildHighlightsTicker(context),
            _buildTabBar(context),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildComptabiliteSection(context),
                  _buildFiscaliteSection(context),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: context.responsivePadding(
        mobile: EdgeInsets.all(DesignTokens.space2),
        tablet: EdgeInsets.all(DesignTokens.space3),
        desktop: EdgeInsets.all(DesignTokens.space4),
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            colorScheme.primary.withValues(alpha: 0.8),
            colorScheme.primary.withValues(alpha: 0.6),
          ],
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(DesignTokens.radius2xl),
          bottomRight: Radius.circular(DesignTokens.radius2xl),
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: DesignTokens.elevation8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'HielCompta',
                      style: context.responsiveText(
                        mobile: AppTypography.h3.copyWith(color: Colors.white),
                        tablet: AppTypography.h2.copyWith(color: Colors.white),
                        desktop: AppTypography.h1.copyWith(color: Colors.white),
                      ),
                    ),
                    SizedBox(height: ResponsiveBreakpoints.getSpacing(
                      context,
                      mobile: DesignTokens.space1,
                      tablet: DesignTokens.space2,
                      desktop: DesignTokens.space3,
                    )),
                    Text(
                      'Votre assistant comptable marocain',
                      style: context.responsiveText(
                        mobile: AppTypography.bodySmall.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                        tablet: AppTypography.bodyMedium.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                        desktop: AppTypography.bodyLarge.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Row( // Wrap icons in a Row
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Profile Button
                  Container(
                    margin: EdgeInsets.only(
                      right: ResponsiveBreakpoints.getSpacing(
                        context,
                        mobile: DesignTokens.space1,
                        tablet: DesignTokens.space2,
                        desktop: DesignTokens.space2,
                      ),
                    ),
                    padding: EdgeInsets.all(
                      ResponsiveBreakpoints.getSpacing(
                        context,
                        mobile: DesignTokens.space1,
                        tablet: DesignTokens.space2,
                        desktop: DesignTokens.space2,
                      ),
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.person_outline, // Profile icon
                        color: Colors.white,
                        size: ResponsiveBreakpoints.getValue(
                          context,
                          mobile: DesignTokens.iconSm,
                          tablet: DesignTokens.iconBase,
                          desktop: DesignTokens.iconLg,
                        ),
                      ),
                      tooltip: 'Voir le profil',
                      onPressed: () {
                        Navigator.pushNamed(context, '/profile'); // Navigate to profile
                      },
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints.tightFor(
                        width: DesignTokens.touchTargetMin,
                        height: DesignTokens.touchTargetMin,
                      ),
                    ),
                  ),
                  // Theme Toggle Button (Original Container)
                  Container(
                    padding: EdgeInsets.all(isVerySmallScreen ? 6 : 8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Provider.of<ThemeService>(context).isDarkMode
                            ? Icons.light_mode
                            : Icons.dark_mode,
                        color: Colors.white,
                        size: isVerySmallScreen ? 18 : (isSmallScreen ? 20 : 24),
                      ),
                      onPressed: () {
                        Provider.of<ThemeService>(context, listen: false).cycleTheme();
                      },
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(
                        minWidth: isVerySmallScreen ? 28 : (isSmallScreen ? 32 : 40),
                        minHeight: isVerySmallScreen ? 28 : (isSmallScreen ? 32 : 40),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 16)),
          _buildSearchBar(context),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600 || size.height < 600;
    final isVerySmallScreen = size.width < 350 || size.height < 500;
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 16), 
        vertical: isVerySmallScreen ? 4 : (isSmallScreen ? 6 : 8)
      ),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.search,
            color: Colors.white.withOpacity(0.8),
            size: isVerySmallScreen ? 16 : (isSmallScreen ? 20 : 24),
          ),
          SizedBox(width: isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 12)),
          Expanded(
            child: Text(
              'Rechercher...',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: Colors.white.withOpacity(0.7),
                fontSize: isVerySmallScreen ? 12 : (isSmallScreen ? 14 : null),
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: isVerySmallScreen ? 4 : (isSmallScreen ? 6 : 8), 
              vertical: isVerySmallScreen ? 1 : (isSmallScreen ? 2 : 4)
            ),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Ctrl+K',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white.withOpacity(0.7),
                fontSize: isVerySmallScreen ? 8 : (isSmallScreen ? 10 : null),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600 || size.height < 600;
    final isVerySmallScreen = size.width < 350 || size.height < 500;

    return Container(
      height: isVerySmallScreen ? 36 : (isSmallScreen ? 40 : 50),
      margin: EdgeInsets.symmetric(
        horizontal: isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 20), 
        vertical: isVerySmallScreen ? 4 : (isSmallScreen ? 6 : 10)
      ),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: colorScheme.primary,
        unselectedLabelColor: colorScheme.onSurfaceVariant,
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        indicator: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withOpacity(0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        tabs: _categories
            .map((category) => Tab(
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: isVerySmallScreen ? 4 : (isSmallScreen ? 8 : 16)
                    ),
                    child: Text(
                      category,
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        fontSize: isVerySmallScreen ? 10 : (isSmallScreen ? 12 : null),
                      ),
                    ),
                  ),
                ))
            .toList(),
      ),
    );
  }

  Widget _buildComptabiliteSection(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600 || size.height < 600;
    final isVerySmallScreen = size.width < 350 || size.height < 500;
    final crossAxisCount = isVerySmallScreen ? 1 : (isSmallScreen ? 2 : 3);
    final childAspectRatio = isVerySmallScreen ? 1.5 : (isSmallScreen ? 1.0 : 1.2);
    
    return GridView.count(
      padding: EdgeInsets.all(isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 16)),
      crossAxisCount: crossAxisCount,
      mainAxisSpacing: isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 16),
      crossAxisSpacing: isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 16),
      childAspectRatio: childAspectRatio,
      children: [
        _buildFeatureCard(
          title: 'PCM',
          subtitle: 'Plan Comptable Marocain',
          icon: Icons.account_balance_outlined,
          color: Theme.of(context).colorScheme.primary,
          onTap: () =>
              setState(() => _selectedItem = NavigationItem.planComptable),
        ),
        _buildFeatureCard(
          title: 'Guide',
          subtitle: 'Guide pratique de comptabilité',
          icon: Icons.menu_book_outlined,
          color: Theme.of(context).colorScheme.tertiary,
          onTap: () => setState(() => _selectedItem = NavigationItem.guide),
        ),
        _buildFeatureCard(
          title: 'Outils',
          subtitle: 'Calculateurs et outils pratiques',
          icon: Icons.calculate_outlined,
          color: Theme.of(context).colorScheme.secondary,
          onTap: () => setState(() => _selectedItem = NavigationItem.tools),
        ),
        _buildFeatureCard(
          title: 'Références',
          subtitle: 'Documents et ressources officiels',
          icon: Icons.library_books_outlined,
          color: Theme.of(context).colorScheme.error,
          onTap: () =>
              setState(() => _selectedItem = NavigationItem.references),
        ),
        SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: QuizProgressWidget(
            onTapQuiz: () => setState(() => _selectedItem = NavigationItem.quiz),
          ),
        ),
         _buildFeatureCard( // Added Exam Card
          title: 'Examens',
          subtitle: 'Entraînez-vous avec des examens',
          icon: Icons.assignment_outlined, // Or Icons.school_outlined
          color: Colors.orange, // Choose a distinct color
          onTap: () => Navigator.pushNamed(context, '/exam_list'),
        ),
      ],
    );
  }

  Widget _buildFiscaliteSection(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600 || size.height < 600;
    final isVerySmallScreen = size.width < 350 || size.height < 500;
    final crossAxisCount = isVerySmallScreen ? 1 : (isSmallScreen ? 2 : 3);
    final childAspectRatio = isVerySmallScreen ? 1.5 : (isSmallScreen ? 1.0 : 1.2);
    
    return GridView.count(
      padding: EdgeInsets.all(isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 16)),
      crossAxisCount: crossAxisCount,
      mainAxisSpacing: isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 16),
      crossAxisSpacing: isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 16),
      childAspectRatio: childAspectRatio,
      children: [
        _buildFeatureCard(
          title: 'IS',
          subtitle: 'Impôt sur les Sociétés',
          icon: Icons.business_outlined,
          color: Theme.of(context).colorScheme.tertiary,
          onTap: () {},
        ),
        _buildFeatureCard(
          title: 'IR',
          subtitle: 'Impôt sur le Revenu',
          icon: Icons.person_outlined,
          color: Theme.of(context).colorScheme.secondary,
          onTap: () {},
        ),
        _buildFeatureCard(
          title: 'TVA',
          subtitle: 'Taxe sur la Valeur Ajoutée',
          icon: Icons.receipt_long_outlined,
          color: Theme.of(context).colorScheme.primary,
          onTap: () {},
        ),
        _buildFeatureCard(
          title: 'DE',
          subtitle: 'Droits d\'Enregistrement',
          icon: Icons.gavel_outlined,
          color: Theme.of(context).colorScheme.error,
          onTap: () {},
        ),
        _buildFeatureCard(
          title: 'Nouveautés',
          subtitle: 'Loi de Finances 2025',
          icon: Icons.new_releases_outlined,
          color: Theme.of(context).colorScheme.tertiary,
          onTap: () {},
        ),
        _buildFeatureCard(
          title: 'Simulateurs',
          subtitle: 'Calcul des impôts',
          icon: Icons.calculate_outlined,
          color: Theme.of(context).colorScheme.secondary,
          onTap: () {},
        ),
      ],
    );
  }

  Widget _buildHighlightsTicker(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600 || size.height < 600;
    final isVerySmallScreen = size.width < 350 || size.height < 500;

    return Container(
      height: isVerySmallScreen ? 32 : (isSmallScreen ? 40 : 50),
      margin: EdgeInsets.symmetric(vertical: isVerySmallScreen ? 2 : (isSmallScreen ? 4 : 8)),
      decoration: BoxDecoration(
        color: colorScheme.primary.withOpacity(0.05),
        border: Border(
          top: BorderSide(color: colorScheme.primary.withOpacity(0.1)),
          bottom: BorderSide(color: colorScheme.primary.withOpacity(0.1)),
        ),
      ),
      child: Row(
        children: [
          // Fixed label section
          Container(
            width: isVerySmallScreen ? 80 : (isSmallScreen ? 100 : 120),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary.withOpacity(0.9),
                  colorScheme.primary.withOpacity(0.7),
                ],
              ),
            ),
            child: Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.newspaper,
                    color: colorScheme.onPrimary,
                    size: isVerySmallScreen ? 12 : (isSmallScreen ? 14 : 18),
                  ),
                  SizedBox(width: isVerySmallScreen ? 2 : (isSmallScreen ? 4 : 8)),
                  Text(
                    'ACTUALITÉS',
                    style: textTheme.labelMedium?.copyWith(
                      color: colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                      fontSize: isVerySmallScreen ? 8 : (isSmallScreen ? 10 : null),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Scrolling content section
          Expanded(
            child: ClipRect(
              child: OverflowBox(
                maxWidth: double.infinity,
                alignment: Alignment.centerLeft,
                child: SlideTransition(
                  position: _tickerAnimation,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: _highlights.map((highlight) {
                      return Container(
                        width: isVerySmallScreen ? 150 : (isSmallScreen ? 200 : 300),
                        padding: EdgeInsets.symmetric(
                          horizontal: isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 16)
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.circle,
                              size: isVerySmallScreen ? 4 : (isSmallScreen ? 6 : 8),
                              color: colorScheme.primary,
                            ),
                            SizedBox(width: isVerySmallScreen ? 4 : (isSmallScreen ? 6 : 8)),
                            Flexible(
                              child: Text(
                                highlight['title']!,
                                style: textTheme.labelMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: colorScheme.primary,
                                  fontSize: isVerySmallScreen ? 8 : (isSmallScreen ? 10 : null),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            SizedBox(width: isVerySmallScreen ? 2 : (isSmallScreen ? 4 : 8)),
                            Flexible(
                              flex: 2,
                              child: Text(
                                highlight['description']!,
                                style: textTheme.bodySmall?.copyWith(
                                  color: colorScheme.onSurfaceVariant,
                                  fontSize: isVerySmallScreen ? 7 : (isSmallScreen ? 9 : null),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 600 || size.height < 600;
    final isVerySmallScreen = size.width < 350 || size.height < 500;

    return Card(
      elevation: 4,
      shadowColor: color.withOpacity(0.2),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.all(isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 12)),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                colorScheme.surface,
                color.withOpacity(0.1),
              ],
            ),
            border: Border.all(
              color: color.withOpacity(0.1),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.all(isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 12)),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      color.withOpacity(0.15),
                      color.withOpacity(0.05),
                    ],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: color.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  size: isVerySmallScreen ? 16 : (isSmallScreen ? 20 : 24),
                  color: color,
                ),
              ),
              SizedBox(height: isVerySmallScreen ? 4 : (isSmallScreen ? 6 : 8)),
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.onSurface,
                  fontSize: isVerySmallScreen ? 10 : (isSmallScreen ? 12 : null),
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: isVerySmallScreen ? 1 : (isSmallScreen ? 2 : 4)),
              Flexible(
                child: Text(
                  subtitle,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    height: 1.1,
                    fontSize: isVerySmallScreen ? 8 : (isSmallScreen ? 10 : null),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: isVerySmallScreen ? 1 : (isSmallScreen ? 1 : 2),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          if (Platform.isWindows) const CustomTitleBar(),
          Expanded(
            child: AdaptiveNavigation(
              selectedItem: _selectedItem,
              onNavigationItemSelected: (item) {
                setState(() => _selectedItem = item);
              },
              child: _buildBody(),
            ),
          ),
        ],
      ),
    );
  }
}
