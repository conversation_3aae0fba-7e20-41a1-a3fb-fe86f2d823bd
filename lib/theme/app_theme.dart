import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'design_tokens.dart';
import 'semantic_colors.dart';
import 'app_typography.dart';

class AppTheme {
  // Legacy constants for backward compatibility - use DesignTokens instead
  static const double _borderRadius = DesignTokens.radiusLg;
  static const double _smallBorderRadius = DesignTokens.radiusBase;
  static const double _cardElevation = DesignTokens.elevation2;
  static const double _headerElevation = DesignTokens.elevation4;
  static const double _spacing = DesignTokens.space4;
  static const double _smallSpacing = DesignTokens.space2;

  static ThemeData getThemeData(ColorScheme colorScheme) {
    // Use the new typography system
    final textTheme = AppTypography.createTextTheme(colorScheme);

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: textTheme,
      // Add semantic colors as theme extension
      extensions: <ThemeExtension<dynamic>>[
        colorScheme.brightness == Brightness.dark
            ? SemanticColors.dark
            : SemanticColors.light,
      ],
      appBarTheme: AppBarTheme(
        centerTitle: true,
        elevation: _headerElevation,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        surfaceTintColor: Colors.transparent,
        shadowColor: colorScheme.shadow.withValues(alpha: 0.1),
      ),
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: colorScheme.surface,
        elevation: _cardElevation,
        selectedIconTheme: IconThemeData(
          color: colorScheme.primary,
          size: 28,
        ),
        unselectedIconTheme: IconThemeData(
          color: colorScheme.onSurfaceVariant,
          size: 24,
        ),
        selectedLabelTextStyle: TextStyle(
          color: colorScheme.primary,
          fontWeight: FontWeight.w600,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
        unselectedLabelTextStyle: TextStyle(
          color: colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
        useIndicator: true,
        indicatorColor: colorScheme.primaryContainer,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
        ),
        minWidth: 72,
        minExtendedWidth: 220,
      ),
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        elevation: _headerElevation,
        shadowColor: colorScheme.shadow.withValues(alpha: 0.1),
        surfaceTintColor: Colors.transparent,
        indicatorColor: colorScheme.primaryContainer,
        labelTextStyle: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return AppTypography.labelMedium.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.w600,
            );
          }
          return AppTypography.labelMedium.copyWith(
            color: colorScheme.onSurfaceVariant,
          );
        }),
      ),
      cardTheme: CardThemeData(
        elevation: _cardElevation,
        shadowColor: colorScheme.shadow.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
        clipBehavior: Clip.antiAlias,
        color: colorScheme.surface,
      ),
      snackBarTheme: SnackBarThemeData(
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
        ),
        elevation: _cardElevation,
        backgroundColor: colorScheme.inverseSurface,
        contentTextStyle: AppTypography.bodyMedium.copyWith(
          color: colorScheme.onInverseSurface,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(
            horizontal: DesignTokens.space6,
            vertical: DesignTokens.space3,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_smallBorderRadius),
          ),
          elevation: _cardElevation,
          shadowColor: colorScheme.shadow.withValues(alpha: 0.2),
          textStyle: AppTypography.buttonMedium,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: DesignTokens.space4,
          vertical: DesignTokens.space4,
        ),
        hintStyle: AppTypography.bodyMedium.copyWith(
          color: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
        ),
      ),
    );
  }

  static BoxDecoration getGradientDecoration(ColorScheme colorScheme) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          colorScheme.primary,
          colorScheme.primary.withValues(alpha: 0.8),
          colorScheme.primaryContainer,
        ],
        stops: const [0.0, 0.5, 1.0],
      ),
      borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
      boxShadow: [
        BoxShadow(
          color: colorScheme.shadow.withValues(alpha: 0.15),
          blurRadius: DesignTokens.elevation12,
          offset: const Offset(0, 4),
          spreadRadius: 2,
        ),
      ],
    );
  }

  static BoxDecoration getModuleIconDecoration(ColorScheme colorScheme, Color baseColor) {
    return BoxDecoration(
      color: baseColor.withValues(alpha: 0.12),
      borderRadius: BorderRadius.circular(DesignTokens.radiusBase),
      border: Border.all(
        color: baseColor.withValues(alpha: 0.05),
        width: 1,
      ),
    );
  }

  static List<Color> getModuleColors(ColorScheme colorScheme) {
    return [
      colorScheme.primary,
      colorScheme.secondary,
      colorScheme.tertiary,
      Color.lerp(colorScheme.primary, colorScheme.secondary, 0.5)!,
      Color.lerp(colorScheme.secondary, colorScheme.tertiary, 0.5)!,
    ];
  }

  static BoxDecoration getCardDecoration(ColorScheme colorScheme) {
    return BoxDecoration(
      color: colorScheme.surface,
      borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
      boxShadow: [
        BoxShadow(
          color: colorScheme.shadow.withValues(alpha: 0.08),
          blurRadius: DesignTokens.elevation8,
          offset: const Offset(0, 3),
          spreadRadius: 1,
        ),
      ],
    );
  }

  static ColorScheme getDarkColorScheme() {
    return ColorScheme.dark(
      primary: const Color(0xFF60A5FA),      // Bright blue - improved contrast
      secondary: const Color(0xFF818CF8),     // Indigo
      tertiary: const Color(0xFFF472B6),     // Pink

      primaryContainer: const Color(0xFF1E3A8A),  // Darker blue - better contrast
      secondaryContainer: const Color(0xFF3730A3), // Darker indigo
      tertiaryContainer: const Color(0xFF9D174D),  // Darker pink

      surface: const Color(0xFF0F172A),       // Darker background - better contrast
      surfaceContainerHighest: const Color(0xFF1E293B),     // Container surface

      error: const Color(0xFFF87171),         // Soft red
      onError: const Color(0xFF000000),       // Black on error for better contrast

      onPrimary: const Color(0xFF000000),     // Black on primary for better contrast
      onSecondary: const Color(0xFF000000),   // Black on secondary
      onTertiary: const Color(0xFF000000),    // Black on tertiary
      onSurface: const Color(0xFFF1F5F9),     // Very light gray - improved contrast
      onSurfaceVariant: const Color(0xFFCBD5E1), // Light gray for variants

      onPrimaryContainer: const Color(0xFFDEEAFF), // Light blue
      onSecondaryContainer: const Color(0xFFE0E7FF), // Light indigo
      onTertiaryContainer: const Color(0xFFFDF2F8), // Light pink

      outline: const Color(0xFF475569),       // Better contrast border
      shadow: const Color(0xFF000000),       // Pure black for shadows
      inverseSurface: const Color(0xFFF8FAFC), // Very light gray
      onInverseSurface: const Color(0xFF0F172A), // Dark background
      inversePrimary: const Color(0xFF2563EB), // Original blue
    );
  }

  static ColorScheme getLightColorScheme() {
    return ColorScheme.light(
      primary: const Color(0xFF2563EB),      // Blue
      secondary: const Color(0xFF4F46E5),     // Indigo
      tertiary: const Color(0xFFDB2777),     // Pink

      primaryContainer: const Color(0xFFDBEAFE),   // Light blue
      secondaryContainer: const Color(0xFFE0E7FF),  // Light indigo
      tertiaryContainer: const Color(0xFFFCE7F3),  // Light pink

      surface: const Color(0xFFFFFFFF),       // Pure white
      surfaceContainerHighest: const Color(0xFFF1F5F9),      // Light gray - better contrast

      error: const Color(0xFFDC2626),         // Red - better contrast
      onError: const Color(0xFFFFFFFF),       // White on error

      onPrimary: const Color(0xFFFFFFFF),     // White on primary
      onSecondary: const Color(0xFFFFFFFF),   // White on secondary
      onTertiary: const Color(0xFFFFFFFF),    // White on tertiary
      onSurface: const Color(0xFF0F172A),     // Very dark gray - improved contrast
      onSurfaceVariant: const Color(0xFF475569), // Medium gray - better contrast

      onPrimaryContainer: const Color(0xFF1E40AF),   // Darker blue
      onSecondaryContainer: const Color(0xFF3730A3), // Darker indigo
      onTertiaryContainer: const Color(0xFF9D174D),  // Darker pink

      outline: const Color(0xFFCBD5E1),       // Better contrast border
      shadow: const Color(0xFF000000),       // Pure black for shadows
      inverseSurface: const Color(0xFF0F172A), // Dark surface
      onInverseSurface: const Color(0xFFF8FAFC), // Light on dark
      inversePrimary: const Color(0xFF60A5FA), // Bright blue
    );
  }
}
