import 'package:flutter/material.dart';

/// Semantic color system using ThemeExtension to provide context-aware colors
/// for different content types with WCAG 2.1 AA compliance.
@immutable
class SemanticColors extends ThemeExtension<SemanticColors> {
  const SemanticColors({
    required this.success,
    required this.successContainer,
    required this.onSuccess,
    required this.onSuccessContainer,
    required this.warning,
    required this.warningContainer,
    required this.onWarning,
    required this.onWarningContainer,
    required this.info,
    required this.infoContainer,
    required this.onInfo,
    required this.onInfoContainer,
    required this.errorVariant,
    required this.errorContainer,
    required this.onErrorVariant,
    required this.onErrorContainer,
    required this.positive,
    required this.positiveContainer,
    required this.onPositive,
    required this.onPositiveContainer,
    required this.negative,
    required this.negativeContainer,
    required this.onNegative,
    required this.onNegativeContainer,
    required this.neutral,
    required this.neutralContainer,
    required this.onNeutral,
    required this.onNeutralContainer,
  });

  // Success colors (for positive actions, confirmations)
  final Color success;
  final Color successContainer;
  final Color onSuccess;
  final Color onSuccessContainer;

  // Warning colors (for cautions, alerts)
  final Color warning;
  final Color warningContainer;
  final Color onWarning;
  final Color onWarningContainer;

  // Info colors (for informational content)
  final Color info;
  final Color infoContainer;
  final Color onInfo;
  final Color onInfoContainer;

  // Error variants (additional error states)
  final Color errorVariant;
  final Color errorContainer;
  final Color onErrorVariant;
  final Color onErrorContainer;

  // Financial data colors
  final Color positive; // For positive financial values
  final Color positiveContainer;
  final Color onPositive;
  final Color onPositiveContainer;

  final Color negative; // For negative financial values
  final Color negativeContainer;
  final Color onNegative;
  final Color onNegativeContainer;

  final Color neutral; // For neutral financial values
  final Color neutralContainer;
  final Color onNeutral;
  final Color onNeutralContainer;

  /// Light theme semantic colors
  static const SemanticColors light = SemanticColors(
    // Success colors - Green variants with proper contrast
    success: Color(0xFF059669), // Green-600
    successContainer: Color(0xFFD1FAE5), // Green-100
    onSuccess: Color(0xFFFFFFFF),
    onSuccessContainer: Color(0xFF064E3B), // Green-900

    // Warning colors - Amber variants
    warning: Color(0xFFD97706), // Amber-600
    warningContainer: Color(0xFFFEF3C7), // Amber-100
    onWarning: Color(0xFFFFFFFF),
    onWarningContainer: Color(0xFF92400E), // Amber-800

    // Info colors - Blue variants
    info: Color(0xFF0284C7), // Sky-600
    infoContainer: Color(0xFFE0F2FE), // Sky-100
    onInfo: Color(0xFFFFFFFF),
    onInfoContainer: Color(0xFF0C4A6E), // Sky-900

    // Error variants - Red variants
    errorVariant: Color(0xFFDC2626), // Red-600
    errorContainer: Color(0xFFFEE2E2), // Red-100
    onErrorVariant: Color(0xFFFFFFFF),
    onErrorContainer: Color(0xFF991B1B), // Red-800

    // Financial positive - Green variants
    positive: Color(0xFF059669), // Green-600
    positiveContainer: Color(0xFFECFDF5), // Green-50
    onPositive: Color(0xFFFFFFFF),
    onPositiveContainer: Color(0xFF064E3B), // Green-900

    // Financial negative - Red variants
    negative: Color(0xFFDC2626), // Red-600
    negativeContainer: Color(0xFFFEF2F2), // Red-50
    onNegative: Color(0xFFFFFFFF),
    onNegativeContainer: Color(0xFF991B1B), // Red-800

    // Financial neutral - Gray variants
    neutral: Color(0xFF6B7280), // Gray-500
    neutralContainer: Color(0xFFF9FAFB), // Gray-50
    onNeutral: Color(0xFFFFFFFF),
    onNeutralContainer: Color(0xFF374151), // Gray-700
  );

  /// Dark theme semantic colors
  static const SemanticColors dark = SemanticColors(
    // Success colors - Brighter green for dark theme
    success: Color(0xFF34D399), // Emerald-400
    successContainer: Color(0xFF064E3B), // Emerald-900
    onSuccess: Color(0xFF000000),
    onSuccessContainer: Color(0xFFD1FAE5), // Emerald-100

    // Warning colors - Brighter amber for dark theme
    warning: Color(0xFFFBBF24), // Amber-400
    warningContainer: Color(0xFF92400E), // Amber-800
    onWarning: Color(0xFF000000),
    onWarningContainer: Color(0xFFFEF3C7), // Amber-100

    // Info colors - Brighter blue for dark theme
    info: Color(0xFF38BDF8), // Sky-400
    infoContainer: Color(0xFF0C4A6E), // Sky-900
    onInfo: Color(0xFF000000),
    onInfoContainer: Color(0xFFE0F2FE), // Sky-100

    // Error variants - Brighter red for dark theme
    errorVariant: Color(0xFFF87171), // Red-400
    errorContainer: Color(0xFF991B1B), // Red-800
    onErrorVariant: Color(0xFF000000),
    onErrorContainer: Color(0xFFFEE2E2), // Red-100

    // Financial positive - Brighter green for dark theme
    positive: Color(0xFF34D399), // Emerald-400
    positiveContainer: Color(0xFF064E3B), // Emerald-900
    onPositive: Color(0xFF000000),
    onPositiveContainer: Color(0xFFECFDF5), // Emerald-50

    // Financial negative - Brighter red for dark theme
    negative: Color(0xFFF87171), // Red-400
    negativeContainer: Color(0xFF991B1B), // Red-800
    onNegative: Color(0xFF000000),
    onNegativeContainer: Color(0xFFFEF2F2), // Red-50

    // Financial neutral - Brighter gray for dark theme
    neutral: Color(0xFF9CA3AF), // Gray-400
    neutralContainer: Color(0xFF374151), // Gray-700
    onNeutral: Color(0xFF000000),
    onNeutralContainer: Color(0xFFF9FAFB), // Gray-50
  );

  @override
  SemanticColors copyWith({
    Color? success,
    Color? successContainer,
    Color? onSuccess,
    Color? onSuccessContainer,
    Color? warning,
    Color? warningContainer,
    Color? onWarning,
    Color? onWarningContainer,
    Color? info,
    Color? infoContainer,
    Color? onInfo,
    Color? onInfoContainer,
    Color? errorVariant,
    Color? errorContainer,
    Color? onErrorVariant,
    Color? onErrorContainer,
    Color? positive,
    Color? positiveContainer,
    Color? onPositive,
    Color? onPositiveContainer,
    Color? negative,
    Color? negativeContainer,
    Color? onNegative,
    Color? onNegativeContainer,
    Color? neutral,
    Color? neutralContainer,
    Color? onNeutral,
    Color? onNeutralContainer,
  }) {
    return SemanticColors(
      success: success ?? this.success,
      successContainer: successContainer ?? this.successContainer,
      onSuccess: onSuccess ?? this.onSuccess,
      onSuccessContainer: onSuccessContainer ?? this.onSuccessContainer,
      warning: warning ?? this.warning,
      warningContainer: warningContainer ?? this.warningContainer,
      onWarning: onWarning ?? this.onWarning,
      onWarningContainer: onWarningContainer ?? this.onWarningContainer,
      info: info ?? this.info,
      infoContainer: infoContainer ?? this.infoContainer,
      onInfo: onInfo ?? this.onInfo,
      onInfoContainer: onInfoContainer ?? this.onInfoContainer,
      errorVariant: errorVariant ?? this.errorVariant,
      errorContainer: errorContainer ?? this.errorContainer,
      onErrorVariant: onErrorVariant ?? this.onErrorVariant,
      onErrorContainer: onErrorContainer ?? this.onErrorContainer,
      positive: positive ?? this.positive,
      positiveContainer: positiveContainer ?? this.positiveContainer,
      onPositive: onPositive ?? this.onPositive,
      onPositiveContainer: onPositiveContainer ?? this.onPositiveContainer,
      negative: negative ?? this.negative,
      negativeContainer: negativeContainer ?? this.negativeContainer,
      onNegative: onNegative ?? this.onNegative,
      onNegativeContainer: onNegativeContainer ?? this.onNegativeContainer,
      neutral: neutral ?? this.neutral,
      neutralContainer: neutralContainer ?? this.neutralContainer,
      onNeutral: onNeutral ?? this.onNeutral,
      onNeutralContainer: onNeutralContainer ?? this.onNeutralContainer,
    );
  }

  @override
  SemanticColors lerp(ThemeExtension<SemanticColors>? other, double t) {
    if (other is! SemanticColors) {
      return this;
    }
    return SemanticColors(
      success: Color.lerp(success, other.success, t)!,
      successContainer: Color.lerp(successContainer, other.successContainer, t)!,
      onSuccess: Color.lerp(onSuccess, other.onSuccess, t)!,
      onSuccessContainer: Color.lerp(onSuccessContainer, other.onSuccessContainer, t)!,
      warning: Color.lerp(warning, other.warning, t)!,
      warningContainer: Color.lerp(warningContainer, other.warningContainer, t)!,
      onWarning: Color.lerp(onWarning, other.onWarning, t)!,
      onWarningContainer: Color.lerp(onWarningContainer, other.onWarningContainer, t)!,
      info: Color.lerp(info, other.info, t)!,
      infoContainer: Color.lerp(infoContainer, other.infoContainer, t)!,
      onInfo: Color.lerp(onInfo, other.onInfo, t)!,
      onInfoContainer: Color.lerp(onInfoContainer, other.onInfoContainer, t)!,
      errorVariant: Color.lerp(errorVariant, other.errorVariant, t)!,
      errorContainer: Color.lerp(errorContainer, other.errorContainer, t)!,
      onErrorVariant: Color.lerp(onErrorVariant, other.onErrorVariant, t)!,
      onErrorContainer: Color.lerp(onErrorContainer, other.onErrorContainer, t)!,
      positive: Color.lerp(positive, other.positive, t)!,
      positiveContainer: Color.lerp(positiveContainer, other.positiveContainer, t)!,
      onPositive: Color.lerp(onPositive, other.onPositive, t)!,
      onPositiveContainer: Color.lerp(onPositiveContainer, other.onPositiveContainer, t)!,
      negative: Color.lerp(negative, other.negative, t)!,
      negativeContainer: Color.lerp(negativeContainer, other.negativeContainer, t)!,
      onNegative: Color.lerp(onNegative, other.onNegative, t)!,
      onNegativeContainer: Color.lerp(onNegativeContainer, other.onNegativeContainer, t)!,
      neutral: Color.lerp(neutral, other.neutral, t)!,
      neutralContainer: Color.lerp(neutralContainer, other.neutralContainer, t)!,
      onNeutral: Color.lerp(onNeutral, other.onNeutral, t)!,
      onNeutralContainer: Color.lerp(onNeutralContainer, other.onNeutralContainer, t)!,
    );
  }
}

/// Extension to easily access semantic colors from BuildContext
extension SemanticColorsExtension on BuildContext {
  SemanticColors get semanticColors => Theme.of(this).extension<SemanticColors>()!;
}
