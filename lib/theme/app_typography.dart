import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'design_tokens.dart';

/// Typography system that extends Google Fonts implementation with consistent
/// text styles and improved hierarchy for the Moroccan Accounting app.
class AppTypography {
  AppTypography._();

  // Base font family
  static String get fontFamily => GoogleFonts.inter().fontFamily!;

  // Display Styles - For large headings and hero text
  static TextStyle get display1 => GoogleFonts.inter(
        fontSize: DesignTokens.fontSize6xl,
        fontWeight: FontWeight.w800,
        height: DesignTokens.lineHeightTight,
        letterSpacing: DesignTokens.letterSpacingTight,
      );

  static TextStyle get display2 => GoogleFonts.inter(
        fontSize: DesignTokens.fontSize5xl,
        fontWeight: FontWeight.w700,
        height: DesignTokens.lineHeightTight,
        letterSpacing: DesignTokens.letterSpacingTight,
      );

  static TextStyle get display3 => GoogleFonts.inter(
        fontSize: DesignTokens.fontSize4xl,
        fontWeight: FontWeight.w600,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  // Heading Styles - For section headers and titles
  static TextStyle get h1 => GoogleFonts.inter(
        fontSize: DesignTokens.fontSize3xl,
        fontWeight: FontWeight.w600,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  static TextStyle get h2 => GoogleFonts.inter(
        fontSize: DesignTokens.fontSize2xl,
        fontWeight: FontWeight.w600,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  static TextStyle get h3 => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeXl,
        fontWeight: FontWeight.w600,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  static TextStyle get h4 => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeLg,
        fontWeight: FontWeight.w600,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  static TextStyle get h5 => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeBase,
        fontWeight: FontWeight.w600,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  static TextStyle get h6 => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeSm,
        fontWeight: FontWeight.w600,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingWide,
      );

  // Body Styles - For regular content
  static TextStyle get bodyLarge => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeBase,
        fontWeight: FontWeight.w400,
        height: DesignTokens.lineHeightRelaxed,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  static TextStyle get bodyMedium => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeSm,
        fontWeight: FontWeight.w400,
        height: DesignTokens.lineHeightRelaxed,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  static TextStyle get bodySmall => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeXs,
        fontWeight: FontWeight.w400,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  // Label Styles - For form labels and UI elements
  static TextStyle get labelLarge => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeSm,
        fontWeight: FontWeight.w500,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingWide,
      );

  static TextStyle get labelMedium => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeXs,
        fontWeight: FontWeight.w500,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingWide,
      );

  static TextStyle get labelSmall => GoogleFonts.inter(
        fontSize: 11.0,
        fontWeight: FontWeight.w500,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingWider,
      );

  // Financial Data Styles - Specialized for accounting numbers
  static TextStyle get financialLarge => GoogleFonts.robotoMono(
        fontSize: DesignTokens.fontSizeLg,
        fontWeight: FontWeight.w600,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  static TextStyle get financialMedium => GoogleFonts.robotoMono(
        fontSize: DesignTokens.fontSizeBase,
        fontWeight: FontWeight.w500,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  static TextStyle get financialSmall => GoogleFonts.robotoMono(
        fontSize: DesignTokens.fontSizeSm,
        fontWeight: FontWeight.w400,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  // Button Styles - For interactive elements
  static TextStyle get buttonLarge => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeBase,
        fontWeight: FontWeight.w600,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingWide,
      );

  static TextStyle get buttonMedium => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeSm,
        fontWeight: FontWeight.w600,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingWide,
      );

  static TextStyle get buttonSmall => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeXs,
        fontWeight: FontWeight.w600,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingWider,
      );

  // Caption and Overline - For supplementary text
  static TextStyle get caption => GoogleFonts.inter(
        fontSize: DesignTokens.fontSizeXs,
        fontWeight: FontWeight.w400,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingNormal,
      );

  static TextStyle get overline => GoogleFonts.inter(
        fontSize: 10.0,
        fontWeight: FontWeight.w500,
        height: DesignTokens.lineHeightNormal,
        letterSpacing: DesignTokens.letterSpacingWider,
      );

  /// Create a complete TextTheme for Material Design
  static TextTheme createTextTheme(ColorScheme colorScheme) {
    return TextTheme(
      displayLarge: display1.copyWith(color: colorScheme.onSurface),
      displayMedium: display2.copyWith(color: colorScheme.onSurface),
      displaySmall: display3.copyWith(color: colorScheme.onSurface),
      headlineLarge: h1.copyWith(color: colorScheme.onSurface),
      headlineMedium: h2.copyWith(color: colorScheme.onSurface),
      headlineSmall: h3.copyWith(color: colorScheme.onSurface),
      titleLarge: h4.copyWith(color: colorScheme.onSurface),
      titleMedium: h5.copyWith(color: colorScheme.onSurface),
      titleSmall: h6.copyWith(color: colorScheme.onSurfaceVariant),
      bodyLarge: bodyLarge.copyWith(color: colorScheme.onSurface),
      bodyMedium: bodyMedium.copyWith(color: colorScheme.onSurface),
      bodySmall: bodySmall.copyWith(color: colorScheme.onSurfaceVariant),
      labelLarge: labelLarge.copyWith(color: colorScheme.onSurface),
      labelMedium: labelMedium.copyWith(color: colorScheme.onSurfaceVariant),
      labelSmall: labelSmall.copyWith(color: colorScheme.onSurfaceVariant),
    );
  }

  /// Get responsive text style based on screen size
  static TextStyle responsive(
    BuildContext context, {
    required TextStyle mobile,
    required TextStyle tablet,
    required TextStyle desktop,
  }) {
    final width = MediaQuery.of(context).size.width;
    if (width < DesignTokens.breakpointMobile) return mobile;
    if (width < DesignTokens.breakpointTablet) return tablet;
    return desktop;
  }
}

/// Extension methods for easy access to typography
extension AppTypographyExtension on BuildContext {
  /// Get typography styles
  AppTypography get typography => AppTypography();
  
  /// Get responsive typography
  TextStyle responsiveText({
    required TextStyle mobile,
    required TextStyle tablet,
    required TextStyle desktop,
  }) {
    return AppTypography.responsive(
      this,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
  }
}
