import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../models/quiz_model.dart';
import '../../theme/semantic_colors.dart';
import '../../theme/design_tokens.dart';
import '../../theme/app_typography.dart';

class QuizOptionButton extends StatelessWidget {
  final QuizQuestion question;
  final int index;
  final bool isSelected;
  final bool showResult;
  final VoidCallback onPressed;
  final Color color;

  const QuizOptionButton({
    super.key,
    required this.question,
    required this.index,
    required this.isSelected,
    required this.showResult,
    required this.onPressed,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final option = question.options[index];
    final isCorrect = index == question.correct;
    final colorScheme = Theme.of(context).colorScheme;
    final semanticColors = context.semanticColors;

    // Determine button color based on state using semantic colors
    Color backgroundColor;
    Color borderColor;
    Color textColor;

    if (showResult) {
      if (isCorrect) {
        backgroundColor = semanticColors.successContainer;
        borderColor = semanticColors.success;
        textColor = semanticColors.onSuccessContainer;
      } else if (isSelected && !isCorrect) {
        backgroundColor = semanticColors.errorContainer;
        borderColor = semanticColors.errorVariant;
        textColor = semanticColors.onErrorContainer;
      } else {
        backgroundColor = colorScheme.surfaceContainerHighest;
        borderColor = colorScheme.outline;
        textColor = colorScheme.onSurfaceVariant;
      }
    } else {
      if (isSelected) {
        backgroundColor = color.withValues(alpha: 0.2);
        borderColor = color;
        textColor = color.withValues(alpha: 0.9);
      } else {
        backgroundColor = colorScheme.surfaceContainerHighest;
        borderColor = colorScheme.outline;
        textColor = colorScheme.onSurfaceVariant;
      }
    }

    return Material(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
      elevation: isSelected ? DesignTokens.elevation2 : DesignTokens.elevation0,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
        child: Container(
          padding: EdgeInsets.all(DesignTokens.space4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(DesignTokens.radiusLg),
            border: Border.all(
              color: borderColor,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: DesignTokens.iconXl + DesignTokens.space1,
                height: DesignTokens.iconXl + DesignTokens.space1,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected
                      ? color.withValues(alpha: 0.2)
                      : colorScheme.surface,
                  border: Border.all(
                    color: isSelected ? color : colorScheme.outline,
                    width: 1.5,
                  ),
                ),
                child: Center(
                  child: Text(
                    String.fromCharCode(65 + index), // A, B, C, D...
                    style: AppTypography.labelLarge.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? color : colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ),
              SizedBox(width: DesignTokens.space4),
              Expanded(
                child: Text(
                  option,
                  style: AppTypography.bodyLarge.copyWith(
                    color: textColor,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                  softWrap: true,
                  overflow: TextOverflow.visible,
                ),
              ),
              if (showResult) ...[
                SizedBox(width: DesignTokens.space2),
                Icon(
                  isCorrect ? Icons.check_circle : Icons.cancel,
                  color: isCorrect
                      ? semanticColors.success
                      : semanticColors.errorVariant,
                  size: DesignTokens.iconBase,
                ),
              ],
            ],
          ),
        ),
      ),
    ).animate()
        .fadeIn()
        .scale()
        .move(delay: DesignTokens.durationNormal * index);
  }
}
